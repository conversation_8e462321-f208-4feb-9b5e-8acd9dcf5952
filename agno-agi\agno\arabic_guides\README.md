# الأدلة العربية لمشروع Agno

مرحباً بك في مجموعة الأدلة العربية لمشروع Agno! هذا المجلد يحتوي على جميع الموارد التي تحتاجها لبناء وكلاء ذكيين باللغة العربية.

## 📁 محتويات المجلد

### 📖 الأدلة والوثائق
- **`agent_building_guide.md`** - الدليل الشامل لبناء الوكلاء
- **`quick_start.md`** - دليل البداية السريعة
- **`README.md`** - هذا الملف

### 🐍 الأمثلة العملية
- **`first_agent_example.py`** - مثال لأول وكيل ذكي
- **`setup_environment.py`** - سكريپت فحص وإعداد البيئة

---

## 🚀 البدء السريع

### 1. فحص البيئة
```bash
python arabic_guides/setup_environment.py
```

### 2. إعداد مفتاح API
```bash
# إنشاء ملف .env في المجلد الرئيسي
echo "OPENAI_API_KEY=your_api_key_here" > .env
```

### 3. تشغيل المثال الأول
```bash
python arabic_guides/first_agent_example.py
```

---

## 📚 ترتيب التعلم الموصى به

### للمبتدئين:
1. اقرأ `quick_start.md`
2. شغل `setup_environment.py`
3. جرب `first_agent_example.py`
4. اقرأ `agent_building_guide.md`

### للمتقدمين:
1. راجع `agent_building_guide.md`
2. استكشف `cookbook/getting_started/`
3. جرب أمثلة `cookbook/examples/`
4. طور مشاريعك الخاصة

---

## 🎯 أهداف التعلم

بعد إكمال هذه الأدلة، ستتمكن من:

✅ إنشاء وكيل ذكي بسيط  
✅ إضافة أدوات البحث للوكيل  
✅ بناء قواعد معرفة متخصصة  
✅ تطوير فرق من الوكلاء  
✅ تخصيص سلوك الوكلاء  
✅ مراقبة أداء الوكلاء  

---

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة:
- Python 3.8+
- pip (مدير الحزم)
- البيئة الافتراضية مفعلة

### المكتبات الأساسية:
- `agno` - المكتبة الرئيسية
- `openai` - نماذج OpenAI
- `duckduckgo-search` - البحث في الويب
- `yfinance` - البيانات المالية

### مفاتيح API:
- OpenAI API Key (مطلوب)
- مفاتيح أخرى اختيارية

---

## 🎨 أمثلة الوكلاء

### وكيل المحادثة البسيط
```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="أنت مساعد ذكي ودود"
)
```

### وكيل البحث والتحليل
```python
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="أنت محلل أخبار متخصص",
    tools=[DuckDuckGoTools()]
)
```

### فريق الوكلاء
```python
from agno.team.team import Team

team = Team(
    members=[research_agent, analysis_agent],
    instructions="اعملوا معاً لتقديم تحليل شامل"
)
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### مشكلة pip
```bash
# استخدم المسار المطلق
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe -m pip install package
```

#### مشكلة مفتاح API
```bash
# تحقق من ملف .env
cat .env
# تأكد من صحة المفتاح
```

#### مشكلة الاستيراد
```bash
# تأكد من تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1
```

---

## 📞 الحصول على المساعدة

### الموارد المتاحة:
1. **الأدلة في هذا المجلد** - ابدأ هنا
2. **أمثلة cookbook** - أمثلة متقدمة
3. **الوثائق الرسمية** - المرجع الكامل
4. **المجتمع** - اطرح أسئلتك

### نصائح للنجاح:
- ابدأ بالأمثلة البسيطة
- اختبر كل خطوة
- اقرأ رسائل الخطأ بعناية
- لا تتردد في طلب المساعدة

---

## 🌟 مشاريع مقترحة

بعد إتقان الأساسيات، جرب هذه المشاريع:

### مشاريع للمبتدئين:
1. **مساعد شخصي** - يجيب على الأسئلة العامة
2. **محلل أخبار** - يلخص الأخبار اليومية
3. **مساعد تعليمي** - يشرح المفاهيم

### مشاريع متوسطة:
1. **نظام خدمة عملاء** - يحل مشاكل العملاء
2. **محلل مالي** - يحلل الأسهم والأسواق
3. **مولد محتوى** - ينشئ مقالات ومنشورات

### مشاريع متقدمة:
1. **فريق بحث متكامل** - عدة وكلاء متخصصين
2. **نظام اتخاذ قرارات** - يحلل ويقترح قرارات
3. **مساعد ذكي متعدد المهام** - يدير مهام معقدة

---

## 📈 التطوير المستمر

### خطة التعلم:
1. **الأسبوع الأول**: الأساسيات والأمثلة البسيطة
2. **الأسبوع الثاني**: الأدوات وقواعد المعرفة
3. **الأسبوع الثالث**: فرق الوكلاء والتخصيص
4. **الأسبوع الرابع**: مشاريع حقيقية

### مؤشرات النجاح:
- إنشاء وكيل يعمل بنجاح
- فهم كيفية إضافة الأدوات
- بناء قاعدة معرفة بسيطة
- تطوير مشروع شخصي

---

**تم إنشاء هذا المجلد في:** 2025-01-17  
**آخر تحديث:** 2025-01-17  
**الإصدار:** 1.0

**مرحباً بك في رحلة بناء الوكلاء الذكيين! 🚀**