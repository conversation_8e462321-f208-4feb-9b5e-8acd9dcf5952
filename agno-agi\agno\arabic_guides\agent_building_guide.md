# دليل بناء الوكلاء في مشروع Agno - باللغة العربية

## 🚀 الخطوات التالية لبناء الوكلاء

بعد أن تم حل مشكلة pip والبيئة الافتراضية، إليك الخطوات التالية لبناء وكلاء ذكيين فعالين:

---

## 📋 المتطلبات الأساسية

### 1. تأكد من تثبيت المتطلبات
```bash
# تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# تثبيت المتطلبات
pip install -r requirements_windows.txt

# تثبيت متطلبات إضافية للوكلاء
pip install openai duckduckgo-search yfinance lancedb tantivy pypdf
```

### 2. إعداد مفاتيح API
```bash
# إنشاء ملف .env في مجلد المشروع
echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
```

---

## 🎯 المستويات التدريجية لبناء الوكلاء

### المستوى 1: الوكيل الأساسي (Basic Agent)
**الهدف:** إنشاء وكيل بسيط بشخصية محددة

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat

# إنشاء وكيل بسيط
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت مساعد ذكي متخصص في التكنولوجيا.
    تجيب بأسلوب ودود ومفيد باللغة العربية.
    """,
    markdown=True,
)

# استخدام الوكيل
agent.print_response("ما هي أحدث تقنيات الذكاء الاصطناعي؟")
```

### المستوى 2: الوكيل مع الأدوات (Agent with Tools)
**الهدف:** إضافة قدرات البحث والوصول للمعلومات

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools

# وكيل مع أدوات البحث
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت محلل أخبار تقني متخصص.
    استخدم أدوات البحث للحصول على أحدث المعلومات.
    قدم تحليلاً شاملاً ومفيداً باللغة العربية.
    """,
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

agent.print_response("ابحث عن أحدث أخبار الذكاء الاصطناعي اليوم")
```

### المستوى 3: الوكيل مع قاعدة المعرفة (Agent with Knowledge)
**الهدف:** إضافة معرفة متخصصة من مصادر محددة

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.lancedb import LanceDb
from agno.embedder.openai import OpenAIEmbedder

# وكيل مع قاعدة معرفة
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت خبير متخصص في المجال المحدد.
    استخدم قاعدة المعرفة للإجابة بدقة.
    """,
    knowledge=PDFUrlKnowledgeBase(
        urls=["path/to/your/document.pdf"],
        vector_db=LanceDb(
            uri="tmp/knowledge_db",
            table_name="specialized_knowledge",
            embedder=OpenAIEmbedder(id="text-embedding-3-small"),
        ),
    ),
    markdown=True,
)

# تحميل قاعدة المعرفة
if agent.knowledge:
    agent.knowledge.load()
```

### المستوى 4: فريق الوكلاء (Agent Team)
**الهدف:** تنسيق عدة وكلاء للعمل معاً

```python
from agno.agent import Agent
from agno.team.team import Team
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.yfinance import YFinanceTools

# وكيل البحث
research_agent = Agent(
    name="باحث",
    role="البحث عن المعلومات",
    model=OpenAIChat(id="gpt-4o"),
    tools=[DuckDuckGoTools()],
    instructions="أنت باحث متخصص في جمع المعلومات الحديثة والموثوقة."
)

# وكيل التحليل المالي
finance_agent = Agent(
    name="محلل مالي",
    role="تحليل البيانات المالية",
    model=OpenAIChat(id="gpt-4o"),
    tools=[YFinanceTools()],
    instructions="أنت محلل مالي خبير في تحليل الأسهم والأسواق."
)

# فريق الوكلاء
team = Team(
    members=[research_agent, finance_agent],
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت منسق فريق التحليل.
    اجمع نتائج الفريق وقدم تقريراً شاملاً.
    """,
    show_tool_calls=True,
    markdown=True,
)

team.print_response("حلل أداء شركة آبل مالياً وإخبارياً")
```

---

## 🛠️ الأدوات المتاحة (Available Tools)

### أدوات البحث والمعلومات
- `DuckDuckGoTools()` - البحث في الويب
- `YFinanceTools()` - البيانات المالية
- `EXATools()` - بحث متقدم

### أدوات المحتوى
- `OpenAITools()` - إنتاج النصوص والصور
- `AnthropicTools()` - نماذج Claude

### أدوات قواعد البيانات
- `LanceDb` - قاعدة بيانات متجهة
- `ChromaDb` - قاعدة بيانات متجهة
- `PgVector` - PostgreSQL مع المتجهات

---

## 📚 أنواع قواعد المعرفة

### 1. من ملفات PDF
```python
from agno.knowledge.pdf import PDFKnowledgeBase

knowledge = PDFKnowledgeBase(
    path="path/to/documents",
    vector_db=LanceDb(uri="tmp/pdf_db")
)
```

### 2. من مواقع الويب
```python
from agno.knowledge.website import WebsiteKnowledgeBase

knowledge = WebsiteKnowledgeBase(
    urls=["https://example.com"],
    vector_db=LanceDb(uri="tmp/web_db")
)
```

### 3. من ملفات نصية
```python
from agno.knowledge.text import TextKnowledgeBase

knowledge = TextKnowledgeBase(
    path="path/to/text/files",
    vector_db=LanceDb(uri="tmp/text_db")
)
```

---

## 🎨 أمثلة عملية للوكلاء

### 1. وكيل خدمة العملاء
```python
customer_service_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت وكيل خدمة عملاء محترف.
    - كن مهذباً ومفيداً
    - اسأل أسئلة توضيحية عند الحاجة
    - قدم حلولاً عملية
    - اتبع سياسات الشركة
    """,
    tools=[DuckDuckGoTools()],
    markdown=True,
)
```

### 2. وكيل التسويق الرقمي
```python
marketing_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت خبير تسويق رقمي.
    - حلل اتجاهات السوق
    - اقترح استراتيجيات تسويقية
    - اكتب محتوى جذاب
    - قدم تحليلات للمنافسين
    """,
    tools=[DuckDuckGoTools()],
    markdown=True,
)
```

### 3. وكيل التعليم والتدريب
```python
education_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="""
    أنت مدرس ذكي ومتخصص.
    - اشرح المفاهيم بطريقة بسيطة
    - استخدم أمثلة عملية
    - تدرج في الصعوبة
    - قدم تمارين وأنشطة
    """,
    knowledge=PDFKnowledgeBase(
        path="educational_materials/",
        vector_db=LanceDb(uri="tmp/education_db")
    ),
    markdown=True,
)
```

---

## 🔧 التخصيص المتقدم

### 1. إعداد الذاكرة
```python
from agno.storage.agent.postgres import PgAgentStorage

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    storage=PgAgentStorage(
        table_name="agent_sessions",
        db_url="postgresql://user:pass@localhost/db"
    ),
    add_history_to_messages=True,
)
```

### 2. إعداد السياق
```python
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    add_datetime_to_instructions=True,
    add_chat_history_to_messages=True,
    num_history_responses=5,
)
```

### 3. إعداد الاستجابات المنظمة
```python
from pydantic import BaseModel

class AnalysisResult(BaseModel):
    summary: str
    key_points: list[str]
    recommendation: str

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    response_model=AnalysisResult,
)
```

---

## 📊 مراقبة الأداء

### 1. تتبع استدعاءات الأدوات
```python
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    show_tool_calls=True,
    debug_mode=True,
)
```

### 2. حفظ السجلات
```python
import logging

logging.basicConfig(level=logging.INFO)
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    monitoring=True,
)
```

---

## 🚀 الخطوات التالية الموصى بها

### 1. ابدأ بالأساسيات
- جرب الأمثلة في `cookbook/getting_started/`
- ابدأ بـ `01_basic_agent.py`
- تدرج إلى `02_agent_with_tools.py`

### 2. اختبر الأدوات المختلفة
- جرب أدوات البحث المختلفة
- اختبر قواعد البيانات المتجهة
- جرب نماذج AI مختلفة

### 3. بناء حالات استخدام حقيقية
- حدد مشكلة تريد حلها
- صمم الوكيل المناسب
- اختبر وحسن الأداء

### 4. تطوير فرق الوكلاء
- ابدأ بوكيلين بسيطين
- اختبر التنسيق بينهما
- طور فرق أكثر تعقيداً

---

## 📁 الملفات المفيدة للمراجعة

### أمثلة أساسية:
- `cookbook/getting_started/01_basic_agent.py`
- `cookbook/getting_started/02_agent_with_tools.py`
- `cookbook/getting_started/03_agent_with_knowledge.py`
- `cookbook/getting_started/05_agent_team.py`

### أمثلة متقدمة:
- `cookbook/examples/agents/` - وكلاء متخصصون
- `cookbook/teams/` - فرق الوكلاء
- `cookbook/workflows/` - سير العمل المعقد

### أدوات وتقنيات:
- `cookbook/tools/` - أدوات مختلفة
- `cookbook/storage/` - حلول التخزين
- `cookbook/models/` - نماذج AI مختلفة

---

## 💡 نصائح للنجاح

1. **ابدأ بسيط** - لا تعقد الأمور في البداية
2. **اختبر كثيراً** - جرب إعدادات مختلفة
3. **راقب الأداء** - تتبع استخدام الأدوات والتكاليف
4. **وثق عملك** - احتفظ بسجل للإعدادات الناجحة
5. **تعلم من الأمثلة** - ادرس الكود الموجود في cookbook

---

**تم إنشاء هذا الدليل في:** 2025-01-17
**الإصدار:** 1.0

للمساعدة أو الاستفسارات، راجع الأمثلة في مجلد `cookbook/` أو اطلب المساعدة!