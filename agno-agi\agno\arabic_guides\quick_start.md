# البداية السريعة - مشروع Agno

## 🚀 الخطوات الأولى

### 1. تفعيل البيئة الافتراضية
```powershell
# في PowerShell
.\env\Scripts\Activate.ps1

# أو في Command Prompt  
.\env\Scripts\activate.bat
```

### 2. فحص الإعداد
```bash
python arabic_guides/setup_environment.py
```

### 3. إعداد مفتاح OpenAI API
```bash
# إنشاء ملف .env
echo "OPENAI_API_KEY=your_actual_api_key_here" > .env
```

### 4. تشغيل أول وكيل
```bash
python arabic_guides/first_agent_example.py
```

---

## 📁 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `arabic_guides/setup_environment.py` | فحص وإعداد البيئة |
| `arabic_guides/first_agent_example.py` | مثال وكيل بسيط |
| `arabic_guides/agent_building_guide.md` | الدليل الشامل |
| `requirements_windows.txt` | المتطلبات (Windows) |

---

## 🎯 أمثلة سريعة

### وكيل بسيط
```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="أنت مساعد ذكي يتحدث العربية"
)

agent.print_response("مرحباً!")
```

### وكيل مع بحث
```python
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions="أنت باحث ذكي",
    tools=[DuckDuckGoTools()]
)

agent.print_response("ابحث عن أخبار الذكاء الاصطناعي")
```

---

## 🆘 حل المشاكل الشائعة

### مشكلة pip
```bash
# استخدم المسار المطلق
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install package_name

# أو
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe -m pip install package_name
```

### مشكلة مفتاح API
```bash
# تأكد من وجود الملف .env
# وأن المفتاح صحيح
cat .env
```

### مشكلة uvloop
```bash
# استخدم requirements_windows.txt بدلاً من requirements.txt
pip install -r requirements_windows.txt
```

---

## 📚 التعلم أكثر

1. **الأساسيات**: `cookbook/getting_started/`
2. **الأمثلة**: `cookbook/examples/`
3. **الأدوات**: `cookbook/tools/`
4. **الفرق**: `cookbook/teams/`

---

## 💡 نصائح سريعة

- ابدأ بالأمثلة البسيطة
- اقرأ الدليل الشامل
- جرب أدوات مختلفة
- اطلب المساعدة عند الحاجة

---

**تاريخ الإنشاء:** 2025-01-17  
**للمساعدة:** راجع `agent_building_guide.md`