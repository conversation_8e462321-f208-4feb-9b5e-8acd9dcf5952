# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.3.5
    # via -r cookbook/demo/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via -r cookbook/demo/requirements.in
anyio==4.9.0
    # via
    #   anthropic
    #   google-genai
    #   groq
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
beautifulsoup4==4.13.4
    # via yfinance
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   duckduckgo-search
    #   rich-toolkit
    #   streamlit
    #   typer
    #   uvicorn
defusedxml==0.7.1
    # via youtube-transcript-api
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
dnspython==2.7.0
    # via email-validator
docstring-parser==0.16
    # via agno
duckduckgo-search==8.0.1
    # via -r cookbook/demo/requirements.in
elevenlabs==1.57.0
    # via -r cookbook/demo/requirements.in
email-validator==2.2.0
    # via fastapi
exa-py==1.12.1
    # via -r cookbook/demo/requirements.in
fastapi==0.115.12
    # via -r cookbook/demo/requirements.in
fastapi-cli==0.0.7
    # via fastapi
frozendict==2.4.6
    # via yfinance
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-auth==2.39.0
    # via google-genai
google-genai==1.11.0
    # via -r cookbook/demo/requirements.in
groq==0.22.0
    # via -r cookbook/demo/requirements.in
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.8
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   elevenlabs
    #   exa-py
    #   fastapi
    #   google-genai
    #   groq
    #   openai
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via
    #   altair
    #   fastapi
    #   pydeck
jiter==0.9.0
    # via
    #   anthropic
    #   openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
lxml==5.3.2
    # via
    #   duckduckgo-search
    #   python-docx
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multitasking==0.0.11
    # via yfinance
narwhals==1.35.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/demo/requirements.in
numpy==2.2.5
    # via
    #   pandas
    #   pgvector
    #   pydeck
    #   streamlit
    #   yfinance
openai==1.75.0
    # via
    #   -r cookbook/demo/requirements.in
    #   exa-py
packaging==24.2
    # via
    #   altair
    #   pytest
    #   streamlit
pandas==2.2.3
    # via
    #   -r cookbook/demo/requirements.in
    #   streamlit
    #   yfinance
peewee==3.17.9
    # via yfinance
pgvector==0.4.0
    # via -r cookbook/demo/requirements.in
pillow==11.2.1
    # via streamlit
platformdirs==4.3.7
    # via yfinance
pluggy==1.5.0
    # via pytest
primp==0.15.0
    # via duckduckgo-search
protobuf==5.29.4
    # via streamlit
psycopg==3.2.6
    # via -r cookbook/demo/requirements.in
psycopg-binary==3.2.6
    # via psycopg
pyarrow==19.0.1
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.3
    # via
    #   agno
    #   anthropic
    #   elevenlabs
    #   exa-py
    #   fastapi
    #   google-genai
    #   groq
    #   openai
    #   pydantic-settings
pydantic-core==2.33.1
    # via
    #   elevenlabs
    #   pydantic
pydantic-settings==2.9.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pypdf==5.4.0
    # via -r cookbook/demo/requirements.in
pytest==8.3.5
    # via pytest-mock
pytest-mock==3.14.0
    # via exa-py
python-dateutil==2.9.0.post0
    # via pandas
python-docx==1.1.2
    # via -r cookbook/demo/requirements.in
python-dotenv==1.1.0
    # via
    #   agno
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via
    #   agno
    #   fastapi
pytz==2025.2
    # via
    #   pandas
    #   yfinance
pyyaml==6.0.2
    # via
    #   agno
    #   uvicorn
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   elevenlabs
    #   exa-py
    #   google-genai
    #   streamlit
    #   yfinance
    #   youtube-transcript-api
rich==14.0.0
    # via
    #   agno
    #   rich-toolkit
    #   typer
rich-toolkit==0.14.1
    # via fastapi-cli
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
shellingham==1.5.4
    # via typer
simplejson==3.20.1
    # via -r cookbook/demo/requirements.in
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.40
    # via -r cookbook/demo/requirements.in
starlette==0.46.2
    # via fastapi
streamlit==1.44.1
    # via -r cookbook/demo/requirements.in
tenacity==9.1.2
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via openai
typer==0.15.2
    # via
    #   agno
    #   fastapi-cli
typing-extensions==4.13.2
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   beautifulsoup4
    #   elevenlabs
    #   exa-py
    #   fastapi
    #   google-genai
    #   groq
    #   openai
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   python-docx
    #   referencing
    #   rich-toolkit
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via
    #   -r cookbook/demo/requirements.in
    #   fastapi
    #   fastapi-cli
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.5
    # via uvicorn
websockets==15.0.1
    # via
    #   elevenlabs
    #   google-genai
    #   uvicorn
yfinance==0.2.55
    # via -r cookbook/demo/requirements.in
youtube-transcript-api==1.0.3
    # via -r cookbook/demo/requirements.in
