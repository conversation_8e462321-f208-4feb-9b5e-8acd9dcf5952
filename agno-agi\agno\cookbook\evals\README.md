# Evals

Evals are used to measure and improve the performance of the Agents you build.

Currently our evals measure three dimensions. You can find a directory with examples for each dimension:

## Accuracy
How accurate is the Agent’s response, given a question and expected response

## Performance
How fast the Agent responds, and what is the memory footprint

## Reliability
How accurate the Agent is making the expected tool calls

