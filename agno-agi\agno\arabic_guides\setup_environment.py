#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريپت إعداد البيئة لمشروع Agno
Environment Setup Script for Agno Project

هذا السكريپت يساعد في إعداد البيئة وفحص المتطلبات
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - مدعوم")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير مدعوم")
        print("يتطلب Python 3.8 أو أحدث")
        return False

def check_virtual_environment():
    """فحص البيئة الافتراضية"""
    print("\n🔧 فحص البيئة الافتراضية...")
    
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ البيئة الافتراضية مفعلة")
        return True
    else:
        print("⚠️  البيئة الافتراضية غير مفعلة")
        print("يرجى تفعيل البيئة الافتراضية أولاً:")
        print(".\env\Scripts\Activate.ps1")
        return False

def check_required_packages():
    """فحص الحزم المطلوبة"""
    print("\n📦 فحص الحزم المطلوبة...")
    
    required_packages = [
        'agno',
        'openai', 
        'duckduckgo-search',
        'yfinance',
        'lancedb',
        'pypdf'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(packages):
    """تثبيت الحزم المفقودة"""
    if not packages:
        return True
    
    print(f"\n🔄 تثبيت {len(packages)} حزمة مفقودة...")
    
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + packages
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع الحزم بنجاح")
            return True
        else:
            print(f"❌ فشل في التثبيت: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def check_api_keys():
    """فحص مفاتيح API"""
    print("\n🔑 فحص مفاتيح API...")
    
    # فحص ملف .env
    env_file = Path('.env')
    if env_file.exists():
        print("✅ ملف .env موجود")
        
        # قراءة محتوى الملف
        with open(env_file, 'r') as f:
            content = f.read()
            
        if 'OPENAI_API_KEY' in content:
            print("✅ OPENAI_API_KEY موجود في .env")
        else:
            print("⚠️  OPENAI_API_KEY غير موجود في .env")
            return False
    else:
        print("⚠️  ملف .env غير موجود")
        
        # إنشاء ملف .env نموذجي
        create_env_file()
        return False
    
    # فحص متغيرات البيئة
    if os.getenv('OPENAI_API_KEY'):
        print("✅ OPENAI_API_KEY موجود في متغيرات البيئة")
        return True
    else:
        print("⚠️  OPENAI_API_KEY غير موجود في متغيرات البيئة")
        return False

def create_env_file():
    """إنشاء ملف .env نموذجي"""
    print("\n📝 إنشاء ملف .env نموذجي...")
    
    env_content = """# مفاتيح API لمشروع Agno
# API Keys for Agno Project

# مفتاح OpenAI API (مطلوب)
# OpenAI API Key (required)
OPENAI_API_KEY=your_openai_api_key_here

# مفاتيح اختيارية (Optional keys)
# ANTHROPIC_API_KEY=your_anthropic_key_here
# GOOGLE_API_KEY=your_google_key_here

# إعدادات قاعدة البيانات (Database settings)
# DATABASE_URL=postgresql://user:password@localhost/dbname
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ تم إنشاء ملف .env")
        print("يرجى تحديث OPENAI_API_KEY بمفتاحك الحقيقي")
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف .env: {e}")

def test_agno_import():
    """اختبار استيراد مكتبة Agno"""
    print("\n🧪 اختبار استيراد Agno...")
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        print("✅ تم استيراد Agno بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد Agno: {e}")
        return False

def create_test_agent():
    """إنشاء وكيل تجريبي"""
    print("\n🤖 اختبار إنشاء وكيل تجريبي...")
    
    if not os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_API_KEY') == 'your_openai_api_key_here':
        print("⚠️  يتطلب مفتاح OpenAI API صحيح للاختبار")
        return False
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        
        agent = Agent(
            model=OpenAIChat(id="gpt-4o"),
            instructions="أنت مساعد ذكي للاختبار. أجب بـ 'مرحباً! الاختبار نجح!' فقط."
        )
        
        print("✅ تم إنشاء الوكيل التجريبي بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الوكيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في سكريپت إعداد بيئة Agno")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # فحص البيئة الافتراضية
    if not check_virtual_environment():
        return False
    
    # فحص الحزم المطلوبة
    missing = check_required_packages()
    
    # تثبيت الحزم المفقودة
    if missing:
        install_choice = input(f"\nهل تريد تثبيت {len(missing)} حزمة مفقودة؟ (y/n): ")
        if install_choice.lower() in ['y', 'yes', 'نعم']:
            if not install_missing_packages(missing):
                return False
        else:
            print("⚠️  لن يعمل المشروع بدون الحزم المطلوبة")
            return False
    
    # اختبار استيراد Agno
    if not test_agno_import():
        return False
    
    # فحص مفاتيح API
    api_keys_ok = check_api_keys()
    
    # اختبار إنشاء وكيل
    if api_keys_ok:
        create_test_agent()
    
    print("\n" + "=" * 50)
    print("🎉 انتهى فحص الإعداد!")
    
    if api_keys_ok:
        print("✅ البيئة جاهزة للاستخدام")
        print("\nيمكنك الآن تشغيل:")
        print("python arabic_guides/first_agent_example.py")
    else:
        print("⚠️  يرجى إعداد مفتاح OpenAI API في ملف .env")
        print("ثم تشغيل هذا السكريپت مرة أخرى")
    
    return True

if __name__ == "__main__":
    main()