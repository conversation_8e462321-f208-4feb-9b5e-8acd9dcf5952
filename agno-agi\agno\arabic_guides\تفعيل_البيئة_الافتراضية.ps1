# سكريبت PowerShell لتفعيل البيئة الافتراضية لمشروع Agno
# PowerShell Script to Activate Agno Virtual Environment

Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "                    مرحباً بك في مشروع Agno                    " -ForegroundColor Yellow
Write-Host "                Welcome to Agno Project                      " -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔄 جاري تفعيل البيئة الافتراضية..." -ForegroundColor Green
Write-Host "🔄 Activating virtual environment..." -ForegroundColor Green

# الحصول على مجلد السكريبت الحالي
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# التحقق من وجود البيئة الافتراضية
$VenvPath = "$ScriptDir\env\Scripts\Activate.ps1"
if (Test-Path $VenvPath) {
    # تفعيل البيئة الافتراضية
    & $VenvPath
    
    Write-Host ""
    Write-Host "✅ تم تفعيل البيئة الافتراضية بنجاح!" -ForegroundColor Green
    Write-Host "✅ Virtual environment activated successfully!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📋 الأوامر المتاحة الآن:" -ForegroundColor Yellow
    Write-Host "📋 Available commands:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "   pip install [package_name]     - تثبيت حزمة جديدة" -ForegroundColor White
    Write-Host "   pip list                       - عرض الحزم المثبتة" -ForegroundColor White
    Write-Host "   pip uninstall [package_name]   - إلغاء تثبيت حزمة" -ForegroundColor White
    Write-Host "   python [script_name.py]       - تشغيل سكريبت Python" -ForegroundColor White
    Write-Host "   deactivate                     - إلغاء تفعيل البيئة الافتراضية" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📁 مسار المشروع:" -ForegroundColor Cyan
    Write-Host "📁 Project path:" -ForegroundColor Cyan
    Write-Host "   $ScriptDir" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "🔧 لتثبيت جميع المتطلبات:" -ForegroundColor Magenta
    Write-Host "🔧 To install all requirements:" -ForegroundColor Magenta
    Write-Host "   pip install -r requirements_windows.txt" -ForegroundColor White
    Write-Host ""
    
    Write-Host "💡 نصيحة: لإلغاء تفعيل البيئة الافتراضية، اكتب: deactivate" -ForegroundColor Yellow
    Write-Host "💡 Tip: To deactivate the virtual environment, type: deactivate" -ForegroundColor Yellow
    Write-Host ""
    
    # التحقق من إصدار Python و pip
    Write-Host "ℹ️  معلومات النظام:" -ForegroundColor Blue
    Write-Host "ℹ️  System Information:" -ForegroundColor Blue
    try {
        $PythonVersion = & python --version 2>&1
        $PipVersion = & pip --version 2>&1
        Write-Host "   Python: $PythonVersion" -ForegroundColor Gray
        Write-Host "   pip: $($PipVersion.Split(' ')[1])" -ForegroundColor Gray
    }
    catch {
        Write-Host "   تعذر الحصول على معلومات الإصدار" -ForegroundColor Red
        Write-Host "   Could not retrieve version information" -ForegroundColor Red
    }
    
} else {
    Write-Host ""
    Write-Host "❌ خطأ: لم يتم العثور على البيئة الافتراضية!" -ForegroundColor Red
    Write-Host "❌ Error: Virtual environment not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 المسار المتوقع:" -ForegroundColor Yellow
    Write-Host "🔍 Expected path:" -ForegroundColor Yellow
    Write-Host "   $VenvPath" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💡 تأكد من وجود مجلد 'env' في نفس مجلد هذا السكريبت" -ForegroundColor Yellow
    Write-Host "💡 Make sure 'env' folder exists in the same directory as this script" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan