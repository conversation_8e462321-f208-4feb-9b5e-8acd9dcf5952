name: Main Validation

on:
  push:
    branches:
      - "*release*" # Run on release-* format
      - "release/*" # Run on release/1.2.3 branch format

jobs:
  style-check:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9"]
      fail-fast: true # Stop all matrix jobs if one fails

    defaults:
      run:
        working-directory: libs/agno

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: "pip"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade \
            pip setuptools wheel \
            ruff mypy pytest types-pyyaml
          pip install --no-deps -r requirements.txt
      - name: Ruff format
        run: |
          ruff format .
      - name: Ruff check
        run: |
          ruff check .
      - name: Mypy
        run: |
          mypy .

  # Run tests for OpenAI
  test-openai:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and OpenAI tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh openai

  # Run tests for Google
  test-google:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      GOOGLE_CLOUD_PROJECT: ${{ secrets.GOOGLE_CLOUD_PROJECT }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Google tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh google

  # Run tests for Anthropic
  test-anthropic:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Anthropic tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh anthropic

  # Run tests for AWS
  test-aws:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.BEDROCK_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.BEDROCK_AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: "us-east-1"
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and AWS tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh aws

  # Run tests for Cohere
  test-cohere:
    if: false # Disable cohere tests until we get a production account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      CO_API_KEY: ${{ secrets.CO_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Cohere tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh cohere

  # Run tests for Deepseek
  test-deepseek:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Deepseek tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh deepseek

  # Run tests for Fireworks
  test-fireworks:
    if: false # Disable fireworks tests until we get a production account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      FIREWORKS_API_KEY: ${{ secrets.FIREWORKS_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run Fireworks tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/models/fireworks

  # Run tests for Groq
  test-groq:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      GROQ_API_KEY: ${{ secrets.GROQ_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Groq tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh groq

  # Run tests for Mistral
  test-mistral:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Mistral tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh mistral

  # Run tests for Nvidia
  test-nvidia:
    if: false # Disable nvidia tests until we get a test account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      NVIDIA_API_KEY: ${{ secrets.NVIDIA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Nvidia tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh nvidia

  # Run tests for OpenRouter
  test-openrouter:
    if: false # Disable openrouter tests until we get a test account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and OpenRouter tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh openrouter

  # Run tests for Perplexity
  test-perplexity:
    if: false # Disable perplexity tests until we get a production account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      PERPLEXITY_API_KEY: ${{ secrets.PERPLEXITY_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Perplexity tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh perplexity

  # Run tests for SambaNova
  test-sambanova:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      SAMBANOVA_API_KEY: ${{ secrets.SAMBANOVA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and SambaNova tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh sambanova

  # Run tests for Together
  test-together:
    if: false # The tests take too long to run
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      TOGETHER_API_KEY: ${{ secrets.TOGETHER_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Together tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh together

  # Run tests for xAI
  test-xai:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      XAI_API_KEY: ${{ secrets.XAI_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and XAI tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh xai

  # Run tests for v0
  test-v0:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      V0_API_KEY: ${{ secrets.V0_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Vercel V0 tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh vercel

  test-ibm-watsonx:
    if: false # Our account is not working
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      IBM_WATSONX_PROJECT_ID: ${{ secrets.IBM_WATSONX_PROJECT_ID }}
      IBM_WATSONX_API_KEY: ${{ secrets.IBM_WATSONX_API_KEY }}
      IBM_WATSONX_URL: ${{ secrets.IBM_WATSONX_URL }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and IBM-WatsonX tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh ibm

  # Run tests for Cerebras
  test-cerebras:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      CEREBRAS_API_KEY: ${{ secrets.CEREBRAS_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Cerebras tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh cerebras

  test-deepinfra:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      DEEPINFRA_API_KEY: ${{ secrets.DEEPINFRA_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup and Deepinfra tests
        working-directory: .
        run: |
          ./scripts/run_model_tests.sh deepinfra

  # Run tests for AI/ML API
  test-aimlapi:
    if: false # We need more credits
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      AIMLAPI_API_KEY: ${{ secrets.AIMLAPI_API_KEY }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run AImlAPI tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/models/aimlapi

  # Run tests for LangDB
  test-langdb:
    if: false # We do not have a company account
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true
    env:
      LANGDB_API_KEY: ${{ secrets.LANGDB_API_KEY }}
      LANGDB_PROJECT_ID: ${{ secrets.LANGDB_PROJECT_ID }}
      EXA_API_KEY: ${{ secrets.EXA_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run LangDB tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/models/langdb

  test-agents:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run agent integration tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/agent

  test-teams:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run teams integration tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/teams

  test-embedder:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      HUGGINGFACE_API_KEY: ${{ secrets.HUGGINGFACE_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run embedder tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/embedder

  test-knowledge:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run knowledge integration tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest ./libs/agno/tests/integration/knowledge

  # Run remaining integration tests
  test-remaining:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: true # Stop all matrix jobs if one fails
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Run dev setup
        working-directory: .
        run: |
          ./scripts/dev_setup.sh
      - name: Run remaining integration tests
        working-directory: .
        run: |
          source .venv/bin/activate
          python -m pytest --ignore=./libs/agno/tests/integration/models --ignore=./libs/agno/tests/integration/agent --ignore=./libs/agno/tests/integration/knowledge --ignore=./libs/agno/tests/integration/teams --ignore=./libs/agno/tests/integration/embedder ./libs/agno/tests/integration
