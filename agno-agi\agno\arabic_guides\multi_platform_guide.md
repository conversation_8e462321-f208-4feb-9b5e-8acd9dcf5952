# دليل استخدام منصات متعددة للذكاء الاصطناعي

## 🌟 نعم! يمكنك استخدام أكثر من منصة

مشروع Agno يدعم العديد من منصات الذكاء الاصطناعي، ويمكنك استخدامها جميعاً في نفس المشروع!

---

## 🔑 المنصات المدعومة

### 1. **OpenAI** (متوفر لديك ✅)
- **النماذج**: GPT-4, GPT-4o, GPT-3.5-turbo
- **المميزات**: دقة عالية، متعدد الاستخدامات
- **التكلفة**: متوسطة إلى عالية

```python
from agno.models.openai import OpenAIChat
agent = Agent(model=OpenAIChat(id="gpt-4o"))
```

### 2. **Anthropic Claude**
- **النماذج**: Claude-3.5-Sonnet, Claude-3-Haiku
- **المميزات**: أمان عالي، تفكير تحليلي
- **التكلفة**: متوسطة

```python
from agno.models.anthropic import AnthropicChat
agent = Agent(model=AnthropicChat(id="claude-3-5-sonnet-20241022"))
```

### 3. **Google Gemini**
- **النماذج**: Gemini-1.5-Pro, Gemini-1.5-Flash
- **المميزات**: متعدد الوسائط، سريع
- **التكلفة**: منخفضة إلى متوسطة

```python
from agno.models.google import GoogleChat
agent = Agent(model=GoogleChat(id="gemini-1.5-pro"))
```

### 4. **Groq** (سريع جداً)
- **النماذج**: Llama-3.1-70B, Mixtral-8x7B
- **المميزات**: سرعة فائقة، مجاني جزئياً
- **التكلفة**: منخفضة جداً

```python
from agno.models.groq import GroqChat
agent = Agent(model=GroqChat(id="llama-3.1-70b-versatile"))
```

### 5. **Cohere**
- **النماذج**: Command-R+, Command-R
- **المميزات**: متخصص في النصوص
- **التكلفة**: متوسطة

```python
from agno.models.cohere import CohereChat
agent = Agent(model=CohereChat(id="command-r-plus"))
```

### 6. **Mistral AI**
- **النماذج**: Mistral-Large, Mistral-Medium
- **المميزات**: أوروبي، متوازن
- **التكلفة**: متوسطة

```python
from agno.models.mistral import MistralChat
agent = Agent(model=MistralChat(id="mistral-large-latest"))
```

---

## 🛠️ إعداد المنصات الإضافية

### 1. تثبيت المكتبات المطلوبة:
```bash
# للحصول على جميع المنصات
pip install anthropic google-generativeai groq cohere mistralai

# أو واحدة تلو الأخرى
pip install anthropic        # لـ Claude
pip install google-generativeai  # لـ Gemini
pip install groq            # لـ Groq
pip install cohere          # لـ Cohere
pip install mistralai       # لـ Mistral
```

### 2. الحصول على مفاتيح API:

#### **Anthropic Claude** (مجاني جزئياً):
1. اذهب إلى: https://console.anthropic.com/
2. أنشئ حساب وادخل إلى Console
3. اذهب إلى "API Keys" واحصل على مفتاح
4. أضف إلى `.env`: `ANTHROPIC_API_KEY=your_key_here`

#### **Google Gemini** (مجاني جزئياً):
1. اذهب إلى: https://aistudio.google.com/
2. أنشئ حساب Google
3. اذهب إلى "Get API Key"
4. أضف إلى `.env`: `GOOGLE_API_KEY=your_key_here`

#### **Groq** (مجاني بحدود عالية):
1. اذهب إلى: https://console.groq.com/
2. أنشئ حساب
3. اذهب إلى "API Keys"
4. أضف إلى `.env`: `GROQ_API_KEY=your_key_here`

#### **Cohere**:
1. اذهب إلى: https://dashboard.cohere.ai/
2. أنشئ حساب
3. احصل على API Key
4. أضف إلى `.env`: `COHERE_API_KEY=your_key_here`

#### **Mistral AI**:
1. اذهب إلى: https://console.mistral.ai/
2. أنشئ حساب
3. احصل على API Key
4. أضف إلى `.env`: `MISTRAL_API_KEY=your_key_here`

---

## 🎯 أمثلة عملية

### مثال 1: وكيل واحد بنماذج مختلفة
```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import AnthropicChat

# وكيل OpenAI
openai_agent = Agent(
    name="OpenAI Assistant",
    model=OpenAIChat(id="gpt-4o"),
    instructions="أنت مساعد دقيق ومفصل"
)

# وكيل Claude
claude_agent = Agent(
    name="Claude Assistant", 
    model=AnthropicChat(id="claude-3-5-sonnet-20241022"),
    instructions="أنت مساعد آمن ومتأني"
)

# استخدام مختلف
openai_agent.print_response("اكتب قصة قصيرة")
claude_agent.print_response("حلل هذا النص أخلاقياً")
```

### مثال 2: فريق من منصات مختلفة
```python
from agno.team.team import Team
from agno.models.openai import OpenAIChat
from agno.models.groq import GroqChat

# وكيل سريع للبحث (Groq)
search_agent = Agent(
    name="باحث سريع",
    model=GroqChat(id="llama-3.1-70b-versatile"),
    tools=[DuckDuckGoTools()],
    instructions="ابحث بسرعة عن المعلومات"
)

# وكيل دقيق للتحليل (OpenAI)
analysis_agent = Agent(
    name="محلل دقيق",
    model=OpenAIChat(id="gpt-4o"),
    instructions="حلل المعلومات بدقة وتفصيل"
)

# فريق مختلط
team = Team(
    members=[search_agent, analysis_agent],
    instructions="اعملوا معاً: ابحثوا ثم حللوا"
)
```

### مثال 3: اختيار النموذج حسب المهمة
```python
def get_best_model_for_task(task_type):
    """اختيار أفضل نموذج حسب نوع المهمة"""
    
    if task_type == "creative":
        # للإبداع: OpenAI
        return OpenAIChat(id="gpt-4o")
    
    elif task_type == "analysis":
        # للتحليل: Claude
        return AnthropicChat(id="claude-3-5-sonnet-20241022")
    
    elif task_type == "speed":
        # للسرعة: Groq
        return GroqChat(id="llama-3.1-70b-versatile")
    
    elif task_type == "multimodal":
        # للوسائط المتعددة: Gemini
        return GoogleChat(id="gemini-1.5-pro")

# استخدام
creative_agent = Agent(
    model=get_best_model_for_task("creative"),
    instructions="أنت كاتب مبدع"
)
```

---

## 💰 مقارنة التكاليف (تقريبية)

| المنصة | التكلفة | السرعة | الجودة | المجاني |
|--------|---------|--------|---------|----------|
| **OpenAI GPT-4** | عالية | متوسطة | ممتازة | محدود |
| **Claude** | متوسطة | متوسطة | ممتازة | محدود |
| **Gemini** | منخفضة | سريعة | جيدة جداً | سخي |
| **Groq** | منخفضة جداً | فائقة | جيدة | سخي جداً |
| **Cohere** | متوسطة | متوسطة | جيدة | محدود |
| **Mistral** | متوسطة | متوسطة | جيدة | محدود |

---

## 🚀 استراتيجيات الاستخدام

### 1. **النهج الهجين** (موصى به):
```python
# استخدم Groq للمهام السريعة
quick_agent = Agent(model=GroqChat(id="llama-3.1-70b-versatile"))

# استخدم GPT-4 للمهام المعقدة
complex_agent = Agent(model=OpenAIChat(id="gpt-4o"))

# استخدم Claude للمهام الحساسة
safe_agent = Agent(model=AnthropicChat(id="claude-3-5-sonnet-20241022"))
```

### 2. **النهج التدريجي**:
```python
def escalate_to_better_model(simple_response, complex_model):
    """ترقية للنموذج الأفضل إذا لم تكن الإجابة كافية"""
    if len(simple_response) < 100:  # إجابة قصيرة
        # استخدم نموذج أقوى
        return complex_model.run("أعد الإجابة بتفصيل أكثر")
    return simple_response
```

### 3. **النهج المتوازي**:
```python
import asyncio

async def get_multiple_opinions(question):
    """احصل على آراء متعددة من نماذج مختلفة"""
    
    agents = [
        Agent(model=OpenAIChat(id="gpt-4o")),
        Agent(model=AnthropicChat(id="claude-3-5-sonnet-20241022")),
        Agent(model=GroqChat(id="llama-3.1-70b-versatile"))
    ]
    
    tasks = [agent.arun(question) for agent in agents]
    responses = await asyncio.gather(*tasks)
    
    return responses
```

---

## 🔧 نصائح للاستخدام الأمثل

### 1. **ابدأ بالمجاني**:
- Groq: سريع ومجاني بحدود عالية
- Gemini: جودة جيدة ومجاني سخي
- ثم انتقل للمدفوع عند الحاجة

### 2. **اختر حسب المهمة**:
- **الكتابة الإبداعية**: OpenAI GPT-4
- **التحليل الأخلاقي**: Claude
- **الاستجابة السريعة**: Groq
- **الوسائط المتعددة**: Gemini

### 3. **راقب التكاليف**:
```python
# تتبع استخدام كل نموذج
usage_tracker = {
    "openai": 0,
    "claude": 0,
    "groq": 0
}

def track_usage(model_name, tokens):
    usage_tracker[model_name] += tokens
    print(f"استخدام {model_name}: {tokens} رمز")
```

---

## 🧪 اختبار المنصات

الآن يمكنك اختبار المثال:

```bash
# تأكد من تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# ثبت المكتبات الإضافية (اختياري)
pip install anthropic google-generativeai groq

# شغل المثال
python arabic_guides/multi_platform_example.py
```

---

## 📝 ملف .env المحدث

ملفك الحالي يحتوي على OpenAI. لإضافة منصات أخرى، أضف مفاتيحها:

```env
# مفتاحك الحالي (يعمل ✅)
OPENAI_API_KEY=sk-1c65a1b5-c81b-4745-b0cb-67cc4e41a03a

# أضف هذه عند الحصول عليها
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here
GROQ_API_KEY=your_groq_key_here
```

---

## 🎉 الخلاصة

**نعم، يمكنك استخدام منصات متعددة!** 

**المميزات:**
- ✅ تنويع المصادر
- ✅ تحسين التكلفة  
- ✅ اختيار الأنسب لكل مهمة
- ✅ تجنب الاعتماد على منصة واحدة

**البداية الموصى بها:**
1. ابدأ بـ OpenAI (لديك مفتاح ✅)
2. أضف Groq (مجاني وسريع)
3. جرب Gemini (مجاني ومتعدد الوسائط)
4. ثم Claude للمهام الحساسة

هل تريد أن نبدأ بتجربة المثال الآن؟