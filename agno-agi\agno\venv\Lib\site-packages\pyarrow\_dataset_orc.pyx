# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# cython: language_level = 3

"""Dataset support for ORC file format."""

from pyarrow.lib cimport *
from pyarrow.includes.libarrow cimport *
from pyarrow.includes.libarrow_dataset cimport *

from pyarrow._dataset cimport FileFormat


cdef class OrcFileFormat(FileFormat):

    def __init__(self):
        self.init(shared_ptr[CFileFormat](new COrcFileFormat()))

    def equals(self, OrcFileFormat other):
        """
        Parameters
        ----------
        other : pyarrow.dataset.OrcFileFormat

        Returns
        -------
        True
        """
        return True

    @property
    def default_extname(self):
        return "orc"

    def __reduce__(self):
        return OrcFileFormat, tuple()
