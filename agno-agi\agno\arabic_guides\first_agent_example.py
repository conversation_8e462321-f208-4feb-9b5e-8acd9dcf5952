#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال عملي لإنشاء أول وكيل ذكي باللغة العربية
Example: Creating your first AI agent in Arabic

هذا المثال يوضح كيفية إنشاء وكيل ذكي بسيط يتحدث العربية
ويمكنه البحث في الويب للحصول على معلومات حديثة.

المتطلبات:
pip install openai duckduckgo-search agno

الاستخدام:
python first_agent_example.py
"""

import os
from textwrap import dedent
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools

def main():
    """الدالة الرئيسية لتشغيل الوكيل"""
    
    print("🤖 مرحباً بك في مثال الوكيل الذكي الأول!")
    print("=" * 50)
    
    # التحقق من وجود مفتاح OpenAI API
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  تحذير: لم يتم العثور على OPENAI_API_KEY")
        print("يرجى إعداد مفتاح API في ملف .env أو متغيرات البيئة")
        print("\nمثال:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        print("أو إنشاء ملف .env مع:")
        print("OPENAI_API_KEY=your-api-key-here")
        return
    
    # إنشاء الوكيل الذكي
    print("🔧 جاري إنشاء الوكيل الذكي...")
    
    agent = Agent(
        model=OpenAIChat(id="gpt-4o"),
        instructions=dedent("""\
            أنت مساعد ذكي متخصص ومفيد يتحدث العربية بطلاقة! 🤖
            
            شخصيتك ومهامك:
            - أنت مساعد ودود ومتحمس للمساعدة
            - تجيب بوضوح ودقة باللغة العربية
            - تستخدم الرموز التعبيرية بشكل مناسب
            - تبحث في الويب عند الحاجة لمعلومات حديثة
            - تقدم إجابات شاملة ومفيدة
            
            أسلوب الإجابة:
            1. ابدأ بتحية مناسبة
            2. قدم الإجابة بشكل منظم
            3. استخدم النقاط والترقيم للوضوح
            4. اختتم بسؤال أو عرض مساعدة إضافية
            
            تذكر:
            - استخدم أدوات البحث للمعلومات الحديثة
            - تأكد من دقة المعلومات
            - كن مفيداً ومتعاوناً دائماً
        """),
        tools=[DuckDuckGoTools()],
        show_tool_calls=True,
        markdown=True,
    )
    
    print("✅ تم إنشاء الوكيل بنجاح!")
    print("\n" + "=" * 50)
    
    # أمثلة للاختبار
    test_questions = [
        "مرحباً! ما هي أحدث أخبار التكنولوجيا اليوم؟",
        "ابحث عن معلومات حول الذكاء الاصطناعي في السعودية",
        "ما هي أفضل لغات البرمجة لتعلم الذكاء الاصطناعي؟",
        "أخبرني عن أحدث تطورات ChatGPT",
    ]
    
    print("🎯 أمثلة للأسئلة التي يمكنك طرحها:")
    for i, question in enumerate(test_questions, 1):
        print(f"{i}. {question}")
    
    print("\n" + "=" * 50)
    
    # تشغيل تفاعلي
    print("💬 يمكنك الآن التحدث مع الوكيل!")
    print("(اكتب 'خروج' أو 'exit' للإنهاء)")
    print("-" * 50)
    
    while True:
        try:
            # الحصول على سؤال من المستخدم
            user_input = input("\n🙋 سؤالك: ").strip()
            
            # التحقق من رغبة المستخدم في الخروج
            if user_input.lower() in ['خروج', 'exit', 'quit', 'bye']:
                print("\n👋 شكراً لك! أراك قريباً!")
                break
            
            if not user_input:
                print("⚠️  يرجى كتابة سؤال أو 'خروج' للإنهاء")
                continue
            
            print(f"\n🤖 الوكيل يفكر ويبحث...")
            print("-" * 30)
            
            # الحصول على إجابة من الوكيل
            agent.print_response(user_input, stream=True)
            
            print("\n" + "-" * 50)
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. أراك قريباً!")
            break
        except Exception as e:
            print(f"\n❌ حدث خطأ: {e}")
            print("يرجى المحاولة مرة أخرى.")

def test_agent():
    """دالة لاختبار الوكيل بسؤال تجريبي"""
    
    print("🧪 اختبار سريع للوكيل...")
    
    agent = Agent(
        model=OpenAIChat(id="gpt-4o"),
        instructions="أنت مساعد ذكي يتحدث العربية. أجب بإيجاز ووضوح.",
        tools=[DuckDuckGoTools()],
        show_tool_calls=True,
        markdown=True,
    )
    
    test_question = "ما هو الطقس اليوم في الرياض؟"
    print(f"السؤال التجريبي: {test_question}")
    print("-" * 40)
    
    agent.print_response(test_question, stream=True)

if __name__ == "__main__":
    # يمكنك تغيير هذا لتشغيل الاختبار السريع بدلاً من الوضع التفاعلي
    interactive_mode = True
    
    if interactive_mode:
        main()
    else:
        test_agent()