# دليل تشغيل واجهة المستخدم لمنصة Agno

## 🎯 نظرة عامة

منصة Agno تحتوي على عدة واجهات مستخدم رائعة مبنية بـ Streamlit. أفضلها هو **Universal Agent Interface (UAgI)** - وهو نظام تشغيل للذكاء الاصطناعي يمكنه التحكم في عدة وكلاء من واجهة واحدة.

---

## 🌟 مميزات Universal Agent Interface

### ✨ القدرات المتوفرة:
- 💬 **محادثة ذكية** مع عدة وكلاء
- 🔍 **البحث في الإنترنت** (DuckDuckGo)
- 🧮 **حاسبة متقدمة** 
- 🐍 **تنفيذ كود Python**
- 💻 **تنفيذ أوامر Shell**
- 📊 **تحليل البيانات المالية**
- 📄 **قراءة ملفات PDF**
- 🗄️ **قاعدة معرفة ذكية**
- 🔄 **تنسيق بين الوكلاء**

### 🎨 واجهة المستخدم:
- 🖥️ **واجهة ويب تفاعلية**
- 📱 **متجاوبة مع الأجهزة المختلفة**
- 🎯 **سهلة الاستخدام**
- ⚡ **سريعة ومتجاوبة**

---

## 🛠️ خطوات التثبيت والتشغيل

### 1. تثبيت Streamlit والمتطلبات
```bash
# تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# تثبيت Streamlit
pip install streamlit

# تثبيت المتطلبات الإضافية
pip install sqlalchemy duckdb tantivy aiofiles beautifulsoup4 pypdf python-docx
```

### 2. التحقق من مفاتيح API
```bash
# تأكد من وجود المفاتيح في ملف .env
cat .env
```

يجب أن يحتوي ملف `.env` على:
```env
# مفاتيح API المطلوبة
GROQ_API_KEY=gsk_your_groq_key_here
GOOGLE_API_KEY=AIzaSy_your_google_key_here

# اختياري (للمزيد من المميزات)
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENAI_API_KEY=your_openai_key_here
```

### 3. تشغيل واجهة المستخدم
```bash
# الانتقال إلى مجلد التطبيق
cd cookbook/examples/streamlit_apps/universal_agent_interface

# تشغيل التطبيق
streamlit run app.py
```

### 4. فتح المتصفح
- افتح المتصفح واذهب إلى: http://localhost:8501
- ستظهر لك واجهة Universal Agent Interface

---

## 🎮 كيفية الاستخدام

### البداية السريعة:
1. **افتح التطبيق** في المتصفح
2. **اختر الوكيل** من القائمة الجانبية
3. **فعل الأدوات** التي تحتاجها
4. **ابدأ المحادثة** في صندوق النص

### أمثلة للتجربة:

#### 🔍 البحث والمعلومات:
- "ابحث عن أحدث أخبار الذكاء الاصطناعي"
- "ما هي أسعار الأسهم اليوم؟"
- "أخبرني عن الطقس في الرياض"

#### 🧮 الحسابات والبرمجة:
- "احسب 15! (15 factorial)"
- "اكتب كود Python لحساب الأعداد الأولية"
- "ما هو الجذر التربيعي لـ 144؟"

#### 📊 تحليل البيانات:
- "حلل أداء سهم Apple في آخر شهر"
- "أنشئ رسم بياني لبيانات المبيعات"
- "قارن بين أداء الأسهم التقنية"

#### 💻 إدارة النظام:
- "تحقق من حالة Docker"
- "اعرض مساحة القرص الصلب"
- "قائمة العمليات النشطة"

---

## 🎨 واجهات أخرى متوفرة

### 1. 🎓 Llama Tutor (مدرس ذكي)
```bash
cd cookbook/examples/streamlit_apps/llama_tutor
streamlit run app.py
```

### 2. 🎮 Game Generator (مولد الألعاب)
```bash
cd cookbook/examples/streamlit_apps/game_generator
streamlit run app.py
```

### 3. 🖼️ Vision AI (ذكاء الرؤية)
```bash
cd cookbook/examples/streamlit_apps/vision_ai
streamlit run app.py
```

### 4. 📊 SQL Agent (وكيل قواعد البيانات)
```bash
cd cookbook/examples/streamlit_apps/sql_agent
streamlit run app.py
```

### 5. 🎵 Podcast Generator (مولد البودكاست)
```bash
cd cookbook/examples/streamlit_apps/podcast_generator
streamlit run app.py
```

---

## 🔧 تخصيص الواجهة

### إضافة وكلاء جدد:
```python
# في ملف agents.py
def create_custom_agent():
    return Agent(
        model=Groq(id="llama-3.1-8b-instant"),
        instructions="أنت وكيل متخصص في...",
        tools=[DuckDuckGoTools()],
    )
```

### تخصيص الأدوات:
```python
# في ملف tools.py
def add_custom_tool():
    # إضافة أداة جديدة
    pass
```

### تغيير التصميم:
```python
# في ملف css.py
def custom_css():
    return """
    <style>
    /* CSS مخصص هنا */
    </style>
    """
```

---

## 🆘 حل المشاكل الشائعة

### مشكلة: "Streamlit not found"
```bash
pip install streamlit
```

### مشكلة: "Module not found"
```bash
# تثبيت المتطلبات المفقودة
pip install -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.txt
```

### مشكلة: "Port already in use"
```bash
# استخدام منفذ مختلف
streamlit run app.py --server.port 8502
```

### مشكلة: "API key not found"
```bash
# تأكد من ملف .env في المجلد الصحيح
cp .env cookbook/examples/streamlit_apps/universal_agent_interface/
```

---

## 🚀 نصائح للاستخدام الأمثل

### 1. **استخدم الأدوات المناسبة**
- فعل أدوات البحث للمعلومات الحديثة
- فعل أدوات الحاسبة للعمليات الرياضية
- فعل أدوات Shell للمهام التقنية

### 2. **اختر الوكيل المناسب**
- وكيل عام للمحادثة العادية
- وكيل متخصص للمهام المحددة
- وكيل بحثي للتحليل المعمق

### 3. **استفد من قاعدة المعرفة**
- أضف ملفات PDF للمراجع
- أضف مواقع ويب للمعلومات
- أضف وثائق للسياق

### 4. **راقب الأداء**
- تابع استخدام API
- راقب سرعة الاستجابة
- تحقق من دقة النتائج

---

## 📱 الوصول من الأجهزة الأخرى

### للوصول من الشبكة المحلية:
```bash
# تشغيل مع إتاحة الوصول الخارجي
streamlit run app.py --server.address 0.0.0.0
```

ثم اذهب إلى: `http://your-computer-ip:8501`

---

## 🎉 الخلاصة

**Universal Agent Interface** هو نظام تشغيل متكامل للذكاء الاصطناعي يوفر:

- ✅ **واجهة موحدة** لعدة وكلاء
- ✅ **أدوات متنوعة** للمهام المختلفة
- ✅ **سهولة الاستخدام** للمبتدئين والخبراء
- ✅ **قابلية التخصيص** للاحتياجات الخاصة
- ✅ **أداء عالي** مع المنصات المجانية

**ابدأ الآن واستمتع بتجربة الذكاء الاصطناعي المتقدم! 🚀**