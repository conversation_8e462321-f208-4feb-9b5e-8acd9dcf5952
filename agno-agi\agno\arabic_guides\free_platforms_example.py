#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال لاستخدام منصات مجانية للذكاء الاصطناعي
Example: Using free AI platforms

هذا المثال يوضح كيفية استخدام منصات مجانية أو شبه مجانية
بدلاً من OpenAI المدفوع
"""

import os
from agno.agent import Agent
from agno.tools.duckduckgo import DuckDuckGoTools

def test_groq_agent():
    """اختبار Groq (مجاني مع حدود عالية)"""
    print("🚀 اختبار Groq (مجاني وسريع)...")
    
    try:
        from agno.models.groq import GroqChat
        
        # تحقق من المفتاح
        if not os.getenv("GROQ_API_KEY"):
            print("⚠️  مفتاح GROQ_API_KEY غير موجود")
            print("احصل على مفتاح مجاني من: https://console.groq.com/")
            return False
        
        agent = Agent(
            model=GroqChat(id="llama-3.1-70b-versatile"),
            instructions="""
            أنت مساعد ذكي يستخدم نموذج Llama عبر Groq.
            أجب باللغة العربية بوضوح وإيجاز.
            """,
            tools=[DuckDuckGoTools()],
            markdown=True,
        )
        
        print("✅ تم إنشاء وكيل Groq بنجاح!")
        
        # اختبار
        response = agent.run("مرحباً! أخبرني عن نفسك")
        print(f"🤖 إجابة Groq: {response.content}")
        return True
        
    except ImportError:
        print("❌ مكتبة Groq غير مثبتة. استخدم: pip install groq")
        return False
    except Exception as e:
        print(f"❌ خطأ في Groq: {e}")
        return False

def test_google_gemini():
    """اختبار Google Gemini (مجاني مع حدود سخية)"""
    print("\n🌟 اختبار Google Gemini (مجاني)...")
    
    try:
        from agno.models.google import GoogleChat
        
        # تحقق من المفتاح
        if not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  مفتاح GOOGLE_API_KEY غير موجود")
            print("احصل على مفتاح مجاني من: https://aistudio.google.com/")
            return False
        
        agent = Agent(
            model=GoogleChat(id="gemini-1.5-flash"),
            instructions="""
            أنت مساعد ذكي يستخدم نموذج Gemini من Google.
            أجب باللغة العربية بطريقة مفيدة وودودة.
            """,
            tools=[DuckDuckGoTools()],
            markdown=True,
        )
        
        print("✅ تم إنشاء وكيل Gemini بنجاح!")
        
        # اختبار
        response = agent.run("ما هي مميزاتك كمساعد ذكي؟")
        print(f"🤖 إجابة Gemini: {response.content}")
        return True
        
    except ImportError:
        print("❌ مكتبة Google غير مثبتة. استخدم: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ خطأ في Gemini: {e}")
        return False

def test_ollama_local():
    """اختبار Ollama (محلي ومجاني تماماً)"""
    print("\n🏠 اختبار Ollama (محلي ومجاني)...")
    
    try:
        from agno.models.ollama import OllamaChat
        
        agent = Agent(
            model=OllamaChat(id="llama3.1"),
            instructions="""
            أنت مساعد ذكي محلي يعمل على جهاز المستخدم.
            أجب باللغة العربية.
            """,
            markdown=True,
        )
        
        print("✅ تم إنشاء وكيل Ollama بنجاح!")
        
        # اختبار
        response = agent.run("مرحباً! كيف حالك؟")
        print(f"🤖 إجابة Ollama: {response.content}")
        return True
        
    except ImportError:
        print("❌ مكتبة Ollama غير مثبتة أو Ollama غير مثبت")
        print("لتثبيت Ollama: https://ollama.ai/")
        return False
    except Exception as e:
        print(f"❌ خطأ في Ollama: {e}")
        print("تأكد من تشغيل Ollama وتحميل نموذج llama3.1")
        return False

def show_free_options():
    """عرض الخيارات المجانية المتاحة"""
    print("🆓 الخيارات المجانية المتاحة:")
    print("=" * 50)
    
    print("\n1. 🚀 Groq (الأفضل للبداية):")
    print("   - مجاني مع حدود عالية")
    print("   - سريع جداً")
    print("   - سهل الإعداد")
    print("   - الموقع: https://console.groq.com/")
    
    print("\n2. 🌟 Google Gemini:")
    print("   - مجاني مع حدود سخية")
    print("   - جودة عالية")
    print("   - متعدد الوسائط")
    print("   - الموقع: https://aistudio.google.com/")
    
    print("\n3. 🏠 Ollama (محلي):")
    print("   - مجاني تماماً")
    print("   - يعمل على جهازك")
    print("   - لا يحتاج إنترنت")
    print("   - الموقع: https://ollama.ai/")
    
    print("\n4. 🤗 Hugging Face:")
    print("   - نماذج مجانية كثيرة")
    print("   - مجتمع نشط")
    print("   - الموقع: https://huggingface.co/")

def interactive_setup():
    """إعداد تفاعلي للمنصات المجانية"""
    print("\n🛠️ إعداد المنصات المجانية:")
    print("=" * 40)
    
    choice = input("""
أي منصة تريد إعدادها؟
1. Groq (سريع ومجاني)
2. Google Gemini (جودة عالية)
3. عرض جميع الخيارات
4. تخطي

اختر رقم (1-4): """).strip()
    
    if choice == "1":
        print("\n🚀 إعداد Groq:")
        print("1. اذهب إلى: https://console.groq.com/")
        print("2. أنشئ حساب مجاني")
        print("3. اذهب إلى 'API Keys'")
        print("4. أنشئ مفتاح جديد")
        print("5. أضف المفتاح إلى ملف .env:")
        print("   GROQ_API_KEY=your_groq_key_here")
        
        key = input("\nإذا كان لديك مفتاح Groq، أدخله هنا (أو اتركه فارغ): ").strip()
        if key:
            update_env_file("GROQ_API_KEY", key)
            test_groq_agent()
    
    elif choice == "2":
        print("\n🌟 إعداد Google Gemini:")
        print("1. اذهب إلى: https://aistudio.google.com/")
        print("2. سجل دخول بحساب Google")
        print("3. اضغط 'Get API Key'")
        print("4. أنشئ مفتاح جديد")
        print("5. أضف المفتاح إلى ملف .env:")
        print("   GOOGLE_API_KEY=your_google_key_here")
        
        key = input("\nإذا كان لديك مفتاح Google، أدخله هنا (أو اتركه فارغ): ").strip()
        if key:
            update_env_file("GOOGLE_API_KEY", key)
            test_google_gemini()
    
    elif choice == "3":
        show_free_options()
    
    else:
        print("تم التخطي.")

def update_env_file(key_name, key_value):
    """تحديث ملف .env بمفتاح جديد"""
    try:
        # قراءة الملف الحالي
        with open('.env', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # البحث عن المفتاح وتحديثه أو إضافته
        key_found = False
        for i, line in enumerate(lines):
            if line.startswith(f"{key_name}="):
                lines[i] = f"{key_name}={key_value}\n"
                key_found = True
                break
        
        if not key_found:
            lines.append(f"\n{key_name}={key_value}\n")
        
        # كتابة الملف المحدث
        with open('.env', 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم تحديث {key_name} في ملف .env")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث ملف .env: {e}")

def main():
    """الدالة الرئيسية"""
    print("🆓 مرحباً بك في مثال المنصات المجانية!")
    print("=" * 50)
    
    # عرض الوضع الحالي
    print("📊 حالة المفاتيح الحالية:")
    
    keys_status = {
        "OPENAI_API_KEY": "❌ غير صحيح",
        "GROQ_API_KEY": "✅ موجود" if os.getenv("GROQ_API_KEY") else "❌ غير موجود",
        "GOOGLE_API_KEY": "✅ موجود" if os.getenv("GOOGLE_API_KEY") else "❌ غير موجود",
    }
    
    for key, status in keys_status.items():
        print(f"  {key}: {status}")
    
    # اختبار المنصات المتوفرة
    print("\n🧪 اختبار المنصات المتوفرة:")
    
    success_count = 0
    
    # اختبار Groq
    if test_groq_agent():
        success_count += 1
    
    # اختبار Gemini
    if test_google_gemini():
        success_count += 1
    
    # اختبار Ollama
    if test_ollama_local():
        success_count += 1
    
    print(f"\n📈 النتيجة: {success_count} منصة تعمل بنجاح")
    
    if success_count == 0:
        print("\n💡 لا توجد منصات تعمل حالياً.")
        interactive_setup()
    else:
        print(f"\n🎉 ممتاز! لديك {success_count} منصة تعمل.")
        print("يمكنك الآن استخدام الوكلاء!")

if __name__ == "__main__":
    # تحميل متغيرات البيئة
    from dotenv import load_dotenv
    load_dotenv()
    
    main()