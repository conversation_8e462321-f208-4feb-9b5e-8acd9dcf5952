#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال لاستخدام منصات متعددة للذكاء الاصطناعي
Example: Using multiple AI platforms

هذا المثال يوضح كيفية استخدام منصات مختلفة في نفس المشروع
"""

import os
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools

# تحميل متغيرات البيئة
from dotenv import load_dotenv
load_dotenv()

def create_openai_agent():
    """إنشاء وكيل باستخدام OpenAI"""
    return Agent(
        name="OpenAI Assistant",
        model=OpenAIChat(id="gpt-4o"),
        instructions="""
        أنت مساعد ذكي يستخدم نموذج OpenAI GPT-4.
        تتميز بالدقة والتفصيل في الإجابات.
        """,
        tools=[DuckDuckGoTools()],
        markdown=True,
    )

def create_anthropic_agent():
    """إنشاء وكيل باستخدام Anthropic Claude"""
    try:
        from agno.models.anthropic import AnthropicChat
        return Agent(
            name="Claude Assistant",
            model=AnthropicChat(id="claude-3-5-sonnet-20241022"),
            instructions="""
            أنت مساعد ذكي يستخدم نموذج Claude من Anthropic.
            تتميز بالتفكير التحليلي والأمان في الإجابات.
            """,
            tools=[DuckDuckGoTools()],
            markdown=True,
        )
    except ImportError:
        print("⚠️  مكتبة Anthropic غير مثبتة. استخدم: pip install anthropic")
        return None

def create_google_agent():
    """إنشاء وكيل باستخدام Google Gemini"""
    try:
        from agno.models.google import GoogleChat
        return Agent(
            name="Gemini Assistant",
            model=GoogleChat(id="gemini-1.5-pro"),
            instructions="""
            أنت مساعد ذكي يستخدم نموذج Gemini من Google.
            تتميز بالقدرة على فهم السياق والمعلومات المتعددة الوسائط.
            """,
            tools=[DuckDuckGoTools()],
            markdown=True,
        )
    except ImportError:
        print("⚠️  مكتبة Google غير مثبتة. استخدم: pip install google-generativeai")
        return None

def create_groq_agent():
    """إنشاء وكيل باستخدام Groq (سريع)"""
    try:
        from agno.models.groq import GroqChat
        return Agent(
            name="Groq Assistant",
            model=GroqChat(id="llama-3.1-70b-versatile"),
            instructions="""
            أنت مساعد ذكي يستخدم نموذج Llama عبر Groq.
            تتميز بالسرعة في الاستجابة والكفاءة.
            """,
            tools=[DuckDuckGoTools()],
            markdown=True,
        )
    except ImportError:
        print("⚠️  مكتبة Groq غير مثبتة. استخدم: pip install groq")
        return None

def compare_models():
    """مقارنة إجابات النماذج المختلفة"""
    print("🔄 مقارنة النماذج المختلفة...")
    print("=" * 60)
    
    # السؤال للمقارنة
    question = "اشرح لي مفهوم الذكاء الاصطناعي في 3 نقاط"
    
    # إنشاء الوكلاء
    agents = []
    
    # OpenAI (متوفر دائماً)
    openai_agent = create_openai_agent()
    if openai_agent:
        agents.append(("OpenAI GPT-4", openai_agent))
    
    # Anthropic Claude (إذا كان متوفراً)
    if os.getenv("ANTHROPIC_API_KEY"):
        claude_agent = create_anthropic_agent()
        if claude_agent:
            agents.append(("Anthropic Claude", claude_agent))
    
    # Google Gemini (إذا كان متوفراً)
    if os.getenv("GOOGLE_API_KEY"):
        gemini_agent = create_google_agent()
        if gemini_agent:
            agents.append(("Google Gemini", gemini_agent))
    
    # Groq (إذا كان متوفراً)
    if os.getenv("GROQ_API_KEY"):
        groq_agent = create_groq_agent()
        if groq_agent:
            agents.append(("Groq Llama", groq_agent))
    
    # تشغيل المقارنة
    for name, agent in agents:
        print(f"\n🤖 إجابة {name}:")
        print("-" * 40)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"❌ خطأ في {name}: {e}")
        print("\n" + "=" * 60)

def interactive_model_selection():
    """اختيار تفاعلي للنموذج"""
    print("🎯 اختر النموذج الذي تريد استخدامه:")
    print("1. OpenAI GPT-4 (متوفر)")
    
    options = [("OpenAI GPT-4", create_openai_agent)]
    option_num = 2
    
    if os.getenv("ANTHROPIC_API_KEY"):
        print(f"{option_num}. Anthropic Claude")
        options.append(("Anthropic Claude", create_anthropic_agent))
        option_num += 1
    
    if os.getenv("GOOGLE_API_KEY"):
        print(f"{option_num}. Google Gemini")
        options.append(("Google Gemini", create_google_agent))
        option_num += 1
    
    if os.getenv("GROQ_API_KEY"):
        print(f"{option_num}. Groq Llama")
        options.append(("Groq Llama", create_groq_agent))
        option_num += 1
    
    print(f"{option_num}. مقارنة جميع النماذج")
    
    try:
        choice = int(input("\nاختر رقم النموذج: "))
        
        if choice == option_num:
            compare_models()
            return
        
        if 1 <= choice <= len(options):
            name, create_func = options[choice - 1]
            agent = create_func()
            
            if agent:
                print(f"\n✅ تم اختيار {name}")
                print("💬 يمكنك الآن التحدث مع الوكيل (اكتب 'خروج' للإنهاء):")
                
                while True:
                    user_input = input("\n🙋 سؤالك: ").strip()
                    
                    if user_input.lower() in ['خروج', 'exit', 'quit']:
                        print("👋 شكراً لك!")
                        break
                    
                    if user_input:
                        print(f"\n🤖 {name} يجيب:")
                        print("-" * 30)
                        agent.print_response(user_input, stream=True)
                        print("-" * 30)
            else:
                print("❌ فشل في إنشاء الوكيل")
        else:
            print("❌ اختيار غير صحيح")
            
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح")
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البرنامج")

def main():
    """الدالة الرئيسية"""
    print("🌟 مرحباً بك في مثال المنصات المتعددة!")
    print("=" * 50)
    
    # فحص المفاتيح المتوفرة
    available_keys = []
    if os.getenv("OPENAI_API_KEY"):
        available_keys.append("OpenAI")
    if os.getenv("ANTHROPIC_API_KEY"):
        available_keys.append("Anthropic")
    if os.getenv("GOOGLE_API_KEY"):
        available_keys.append("Google")
    if os.getenv("GROQ_API_KEY"):
        available_keys.append("Groq")
    
    print(f"🔑 المنصات المتوفرة: {', '.join(available_keys)}")
    
    if len(available_keys) == 0:
        print("❌ لا توجد مفاتيح API متوفرة")
        return
    
    print("\nماذا تريد أن تفعل؟")
    print("1. اختيار نموذج واحد للمحادثة")
    print("2. مقارنة جميع النماذج المتوفرة")
    
    try:
        choice = input("\nاختر (1 أو 2): ").strip()
        
        if choice == "1":
            interactive_model_selection()
        elif choice == "2":
            compare_models()
        else:
            print("❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البرنامج")

if __name__ == "__main__":
    main()