name: "<PERSON>ale Issues"

on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write

    steps:
      - uses: actions/stale@v9
        with:
          stale-issue-message: >
            This issue has been automatically marked as stale due to 30 days of inactivity.

          # Mark issues as stale after 30 days
          days-before-issue-stale: 30

          # Effectively never close issues
          days-before-issue-close: 99999

          stale-issue-label: "stale"
          remove-stale-when-updated: true
