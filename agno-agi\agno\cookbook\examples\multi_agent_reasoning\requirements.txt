# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.5.6
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
annotated-types==0.7.0
    # via pydantic
anthropic==0.52.1
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
beautifulsoup4==4.13.4
    # via yfinance
certifi==2025.4.26
    # via
    #   curl-cffi
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via curl-cffi
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   duckduckgo-search
    #   rich-toolkit
    #   typer
    #   uvicorn
curl-cffi==0.11.1
    # via yfinance
distro==1.9.0
    # via
    #   anthropic
    #   openai
dnspython==2.7.0
    # via email-validator
docstring-parser==0.16
    # via agno
duckduckgo-search==8.0.2
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
email-validator==2.2.0
    # via fastapi
fastapi==0.115.12
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
fastapi-cli==0.0.7
    # via fastapi
frozendict==2.4.6
    # via yfinance
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via agno
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   fastapi
    #   openai
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
jinja2==3.1.6
    # via fastapi
jiter==0.10.0
    # via
    #   anthropic
    #   openai
lxml==5.4.0
    # via duckduckgo-search
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multitasking==0.0.11
    # via yfinance
numpy==2.2.6
    # via
    #   pandas
    #   pgvector
    #   yfinance
openai==1.82.1
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
pandas==2.2.3
    # via yfinance
peewee==3.18.1
    # via yfinance
pgvector==0.4.1
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
platformdirs==4.3.8
    # via yfinance
primp==0.15.0
    # via duckduckgo-search
protobuf==6.31.1
    # via yfinance
psycopg==3.2.9
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
psycopg-binary==3.2.9
    # via psycopg
pycparser==2.22
    # via cffi
pydantic==2.11.5
    # via
    #   agno
    #   anthropic
    #   fastapi
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via agno
pygments==2.19.1
    # via rich
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.0
    # via
    #   agno
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via
    #   agno
    #   fastapi
pytz==2025.2
    # via
    #   pandas
    #   yfinance
pyyaml==6.0.2
    # via
    #   agno
    #   uvicorn
requests==2.32.3
    # via yfinance
rich==14.0.0
    # via
    #   agno
    #   rich-toolkit
    #   typer
rich-toolkit==0.14.7
    # via fastapi-cli
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.41
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
starlette==0.46.2
    # via fastapi
tomli==2.2.1
    # via agno
tqdm==4.67.1
    # via openai
typer==0.16.0
    # via
    #   agno
    #   fastapi-cli
typing-extensions==4.13.2
    # via
    #   agno
    #   anthropic
    #   anyio
    #   beautifulsoup4
    #   fastapi
    #   openai
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   rich-toolkit
    #   sqlalchemy
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via
    #   fastapi
    #   fastapi-cli
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.5
    # via uvicorn
websockets==15.0.1
    # via
    #   uvicorn
    #   yfinance
yfinance==0.2.61
    # via -r cookbook/examples/multi_agent_reasoning/requirements.in
