# دليل استخدام مشروع Agno - باللغة العربية

## نظرة عامة
هذا الدليل يشرح كيفية استخدام البيئة الافتراضية ومدير الحزم pip في مشروع Agno على نظام Windows.

## المشكلة التي تم حلها
كانت هناك مشكلة في مسار البيئة الافتراضية حيث كان pip يحاول الوصول إلى مسار خاطئ:
- المسار الخاطئ: `C:\Users\<USER>\agno-agi\env\Scripts\python.exe`
- المسار الصحيح: `C:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe`

## الحلول المتاحة

### الطريقة الأولى: استخدام المسار المطلق لـ pip
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install اسم_الحزمة
```

**مثال:**
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install requests
```

### الطريقة الثانية: استخدام python -m pip
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe -m pip install اسم_الحزمة
```

**مثال:**
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe -m pip install numpy
```

### الطريقة الثالثة: تفعيل البيئة الافتراضية أولاً (الأسهل)
```powershell
# 1. انتقل إلى مجلد المشروع
cd c:\Users\<USER>\agno-agi\agno

# 2. فعل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# 3. الآن يمكنك استخدام pip بشكل طبيعي
pip install اسم_الحزمة
```

### الطريقة الرابعة: استخدام الملفات المساعدة الجاهزة

#### للـ PowerShell:
```powershell
.\activate_env.ps1
```

#### للـ Command Prompt:
```cmd
.\activate_env.bat
```

## تثبيت المتطلبات

### تثبيت جميع المتطلبات (النسخة المتوافقة مع Windows):
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install -r requirements_windows.txt
```

### تثبيت المتطلبات بعد تفعيل البيئة الافتراضية:
```powershell
# فعل البيئة الافتراضية أولاً
.\env\Scripts\Activate.ps1

# ثم ثبت المتطلبات
pip install -r requirements_windows.txt
```

## أوامر مفيدة

### التحقق من إصدار pip:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe --version
```

### التحقق من إصدار Python:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe --version
```

### عرض الحزم المثبتة:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe list
```

### البحث عن حزمة:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe search اسم_الحزمة
```

### إلغاء تثبيت حزمة:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe uninstall اسم_الحزمة
```

### تحديث حزمة:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install --upgrade اسم_الحزمة
```

## ملاحظات مهمة

### 1. مشكلة uvloop على Windows
- مكتبة `uvloop` لا تدعم نظام Windows
- تم إنشاء ملف `requirements_windows.txt` بدون هذه المكتبة
- استخدم `requirements_windows.txt` بدلاً من `requirements.txt` على Windows

### 2. تفعيل البيئة الافتراضية
عند تفعيل البيئة الافتراضية، ستلاحظ ظهور `(env)` في بداية سطر الأوامر:
```
(env) PS C:\Users\<USER>\agno-agi\agno>
```

### 3. إلغاء تفعيل البيئة الافتراضية
لإلغاء تفعيل البيئة الافتراضية، اكتب:
```powershell
deactivate
```

## استكشاف الأخطاء وإصلاحها

### إذا ظهرت رسالة خطأ "The system cannot find the file specified"
- تأكد من استخدام المسار الكامل للملفات
- تأكد من أن البيئة الافتراضية مفعلة بشكل صحيح

### إذا فشل تثبيت حزمة معينة
- جرب تحديث pip أولاً:
```powershell
c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe -m pip install --upgrade pip
```

### إذا كانت هناك مشاكل في الأذونات
- تأكد من تشغيل PowerShell كمدير (Run as Administrator)

## مسارات مهمة

- **مجلد المشروع:** `c:\Users\<USER>\agno-agi\agno\`
- **البيئة الافتراضية:** `c:\Users\<USER>\agno-agi\agno\env\`
- **Python:** `c:\Users\<USER>\agno-agi\agno\env\Scripts\python.exe`
- **pip:** `c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe`
- **ملف المتطلبات (Windows):** `c:\Users\<USER>\agno-agi\agno\requirements_windows.txt`

## أمثلة عملية

### مثال 1: تثبيت حزمة جديدة
```powershell
# الطريقة السريعة
c:\Users\<USER>\agno-agi\agno\env\Scripts\pip.exe install matplotlib

# أو بعد تفعيل البيئة الافتراضية
cd c:\Users\<USER>\agno-agi\agno
.\env\Scripts\Activate.ps1
pip install matplotlib
```

### مثال 2: تشغيل سكريبت Python
```powershell
# تفعيل البيئة الافتراضية
cd c:\Users\<USER>\agno-agi\agno
.\env\Scripts\Activate.ps1

# تشغيل السكريبت
python اسم_الملف.py
```

### مثال 3: إنشاء ملف requirements جديد
```powershell
# تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# إنشاء ملف requirements
pip freeze > my_requirements.txt
```

---

**تم إنشاء هذا الدليل في:** $(Get-Date)
**الإصدار:** 1.0
**المؤلف:** مساعد الذكي الاصطناعي

للمساعدة أو الاستفسارات، يرجى الرجوع إلى هذا الدليل أو طلب المساعدة من فريق الدعم.