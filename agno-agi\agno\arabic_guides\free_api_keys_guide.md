# دليل الحصول على مفاتيح API مجانية

## 🆓 المنصات المجانية الموصى بها

### 1. 🚀 Groq (الأفضل للبداية)

**المميزات:**
- ✅ مجاني مع حدود عالية (30,000 رمز/دقيقة)
- ✅ سريع جداً (أسرع من OpenAI)
- ✅ نماذج قوية (Llama 3.1, Mixtral)
- ✅ سهل الإعداد

**خطوات الحصول على المفتاح:**
1. اذهب إلى: https://console.groq.com/
2. اضغط "Sign Up" وأنشئ حساب
3. تأكد من بريدك الإلكتروني
4. اذهب إلى "API Keys" من القائمة الجانبية
5. اضغط "Create API Key"
6. أعطه اسم (مثل: "Agno Project")
7. ان<PERSON>خ المفتاح وأضفه إلى `.env`:
   ```
   GROQ_API_KEY=gsk_your_key_here
   ```

**الاستخدام:**
```python
from agno.models.groq import GroqChat
agent = Agent(model=GroqChat(id="llama-3.1-70b-versatile"))
```

---

### 2. 🌟 Google Gemini (جودة عالية)

**المميزات:**
- ✅ مجاني مع حدود سخية (15 طلب/دقيقة)
- ✅ جودة ممتازة
- ✅ متعدد الوسائط (نص، صور، فيديو)
- ✅ من Google

**خطوات الحصول على المفتاح:**
1. اذهب إلى: https://aistudio.google.com/
2. سجل دخول بحساب Google الخاص بك
3. اضغط "Get API Key" في الأعلى
4. اختر "Create API key in new project"
5. انسخ المفتاح وأضفه إلى `.env`:
   ```
   GOOGLE_API_KEY=AIzaSy_your_key_here
   ```

**الاستخدام:**
```python
from agno.models.google import GoogleChat
agent = Agent(model=GoogleChat(id="gemini-1.5-flash"))
```

---

### 3. 🏠 Ollama (محلي ومجاني تماماً)

**المميزات:**
- ✅ مجاني تماماً (لا حدود)
- ✅ يعمل على جهازك
- ✅ لا يحتاج إنترنت
- ✅ خصوصية كاملة

**خطوات التثبيت:**
1. اذهب إلى: https://ollama.ai/
2. حمل Ollama لنظام Windows
3. ثبت البرنامج
4. افتح Command Prompt وشغل:
   ```bash
   ollama pull llama3.1
   ```
5. لا تحتاج مفتاح API!

**الاستخدام:**
```python
from agno.models.ollama import OllamaChat
agent = Agent(model=OllamaChat(id="llama3.1"))
```

---

### 4. 🤗 Hugging Face (نماذج متنوعة)

**المميزات:**
- ✅ مجاني مع حدود معقولة
- ✅ نماذج متنوعة
- ✅ مجتمع نشط
- ✅ مفتوح المصدر

**خطوات الحصول على المفتاح:**
1. اذهب إلى: https://huggingface.co/
2. أنشئ حساب مجاني
3. اذهب إلى Settings → Access Tokens
4. أنشئ token جديد
5. أضفه إلى `.env`:
   ```
   HUGGINGFACE_API_KEY=hf_your_key_here
   ```

---

## 🔧 إعداد سريع

### تثبيت المكتبات المطلوبة:
```bash
# تفعيل البيئة الافتراضية
.\env\Scripts\Activate.ps1

# تثبيت المكتبات
pip install groq google-generativeai
```

### تحديث ملف .env:
```env
# مفتاحك الحالي (غير صحيح)
OPENAI_API_KEY=sk-1c65a1b5-c81b-4745-b0cb-67cc4e41a03a

# مفاتيح مجانية (أضف ما تحصل عليه)
GROQ_API_KEY=gsk_your_groq_key_here
GOOGLE_API_KEY=AIzaSy_your_google_key_here
```

---

## 🧪 اختبار المنصات

```bash
# اختبار المنصات المجانية
python arabic_guides/free_platforms_example.py
```

---

## 💰 مقارنة سريعة

| المنصة | التكلفة | السرعة | الجودة | سهولة الإعداد |
|--------|---------|--------|---------|----------------|
| **Groq** | مجاني | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Gemini** | مجاني | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Ollama** | مجاني | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **OpenAI** | مدفوع | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

---

## 🎯 التوصية

**للبداية السريعة:**
1. **ابدأ بـ Groq** - سريع ومجاني وسهل
2. **أضف Gemini** - للجودة العالية
3. **جرب Ollama** - للاستخدام المحلي
4. **احصل على OpenAI لاحقاً** - للمشاريع المهمة

---

## 🚀 البدء الآن

```bash
# 1. احصل على مفتاح Groq (5 دقائق)
# https://console.groq.com/

# 2. أضفه إلى .env
echo "GROQ_API_KEY=your_key_here" >> .env

# 3. اختبر
python arabic_guides/free_platforms_example.py
```

---

## 🆘 حل المشاكل

### مشكلة: "مفتاح OpenAI غير صحيح"
**الحل:** استخدم منصة مجانية بدلاً منه

### مشكلة: "مكتبة غير مثبتة"
**الحل:** 
```bash
pip install groq google-generativeai
```

### مشكلة: "لا توجد منصات تعمل"
**الحل:** تأكد من:
- تفعيل البيئة الافتراضية
- إضافة المفاتيح إلى `.env`
- تثبيت المكتبات المطلوبة

---

**نصيحة:** ابدأ بـ Groq - ستحصل على نتائج ممتازة مجاناً! 🚀