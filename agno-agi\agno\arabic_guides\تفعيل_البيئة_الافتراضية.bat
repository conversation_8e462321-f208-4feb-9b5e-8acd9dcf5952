@echo off
chcp 65001 >nul
title تفعيل البيئة الافتراضية - Agno Project

echo.
echo ═══════════════════════════════════════════════════════════════
echo                     مرحباً بك في مشروع Agno                    
echo                  Welcome to Agno Project                     
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔄 جاري تفعيل البيئة الافتراضية...
echo 🔄 Activating virtual environment...
echo.

REM التحقق من وجود البيئة الافتراضية
if exist "%~dp0env\Scripts\activate.bat" (
    call "%~dp0env\Scripts\activate.bat"
    
    echo.
    echo ✅ تم تفعيل البيئة الافتراضية بنجاح!
    echo ✅ Virtual environment activated successfully!
    echo.
    
    echo 📋 الأوامر المتاحة الآن:
    echo 📋 Available commands:
    echo.
    echo    pip install [package_name]     - تثبيت حزمة جديدة
    echo    pip list                       - عرض الحزم المثبتة  
    echo    pip uninstall [package_name]   - إلغاء تثبيت حزمة
    echo    python [script_name.py]       - تشغيل سكريبت Python
    echo    deactivate                     - إلغاء تفعيل البيئة الافتراضية
    echo.
    
    echo 📁 مسار المشروع:
    echo 📁 Project path:
    echo    %~dp0
    echo.
    
    echo 🔧 لتثبيت جميع المتطلبات:
    echo 🔧 To install all requirements:
    echo    pip install -r requirements_windows.txt
    echo.
    
    echo 💡 نصيحة: لإلغاء تفعيل البيئة الافتراضية، اكتب: deactivate
    echo 💡 Tip: To deactivate the virtual environment, type: deactivate
    echo.
    
    echo ℹ️  معلومات النظام:
    echo ℹ️  System Information:
    python --version 2>nul && pip --version 2>nul
    echo.
    
) else (
    echo.
    echo ❌ خطأ: لم يتم العثور على البيئة الافتراضية!
    echo ❌ Error: Virtual environment not found!
    echo.
    echo 🔍 المسار المتوقع:
    echo 🔍 Expected path:
    echo    %~dp0env\Scripts\activate.bat
    echo.
    echo 💡 تأكد من وجود مجلد 'env' في نفس مجلد هذا الملف
    echo 💡 Make sure 'env' folder exists in the same directory as this file
    echo.
    pause
    exit /b 1
)

echo ═══════════════════════════════════════════════════════════════
echo.
echo اضغط أي مفتاح للمتابعة أو اكتب الأوامر مباشرة...
echo Press any key to continue or type commands directly...
cmd /k